<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Quick Language Switch Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .language-switcher { margin: 20px 0; }
        .btn { padding: 8px 16px; margin: 5px; text-decoration: none; border: 1px solid #007bff; color: #007bff; }
        .btn:hover { background: #007bff; color: white; }
        .active { background: #28a745 !important; color: white !important; border-color: #28a745 !important; }
        #log { background: #f8f9fa; padding: 10px; margin: 20px 0; font-family: monospace; white-space: pre-wrap; }
    </style>
</head>
<body>
    <h1>Quick Test</h1>
    
    <p>Click the buttons below to test language switching:</p>
    
    <div class="language-switcher Rqst-btn">
        <a href="#" class="btn btn-outline-primary btn-sm">中文版</a>
        <a href="#" class="btn btn-outline-primary btn-sm">EN</a>
    </div>
    
    <div id="log">Waiting for clicks...</div>
    
    <p>Test links:</p>
    <a href="cn_website/index.html">Go to Chinese site</a> | 
    <a href="en_website/index.html">Go to English site</a>

    <script src="en_website/assets/js/jquery.min.js"></script>
    <script>
        function log(msg) {
            document.getElementById('log').textContent += new Date().toLocaleTimeString() + ': ' + msg + '\n';
            console.log(msg);
        }
        
        $(document).ready(function() {
            log('jQuery ready');
            log('Found ' + $('.language-switcher a').length + ' buttons');
            
            $('.language-switcher a').click(function(e) {
                e.preventDefault();
                var lang = $(this).text().trim();
                log('Clicked: ' + lang);
                
                var currentPage = window.location.pathname.split('/').pop() || 'index.html';
                log('Current page: ' + currentPage);
                
                var currentPath = window.location.pathname;
                log('Current path: ' + currentPath);
                
                var basePath = '';
                if (currentPath.includes('/en_website/')) {
                    basePath = currentPath.substring(0, currentPath.lastIndexOf('/en_website/'));
                } else if (currentPath.includes('/cn_website/')) {
                    basePath = currentPath.substring(0, currentPath.lastIndexOf('/cn_website/'));
                } else {
                    basePath = window.location.origin + window.location.pathname.substring(0, window.location.pathname.lastIndexOf('/'));
                    if (basePath === window.location.origin) {
                        basePath = '.';
                    }
                }
                log('Base path: ' + basePath);
                
                if (lang === '中文版') {
                    var url = basePath + '/cn_website/' + currentPage;
                    log('Would go to: ' + url);
                    $(this).addClass('active');
                    setTimeout(() => {
                        log('Redirecting now...');
                        window.location.href = url;
                    }, 1000);
                } else if (lang === 'EN') {
                    var url = basePath + '/en_website/' + currentPage;
                    log('Would go to: ' + url);
                    $(this).addClass('active');
                    setTimeout(() => {
                        log('Redirecting now...');
                        window.location.href = url;
                    }, 1000);
                }
            });
            
            log('Event handlers attached');
        });
    </script>
</body>
</html>
