# YK-EcoPack 静态网站

这是基于原WordPress网站重新构建的纯HTML + CSS + JavaScript静态网站。

## 项目结构

```
static-website/
├── index.html                  # 主页
├── about.html                  # 公司信息页面
├── contact.html                # 联系我们页面
├── kitchen-garbage-bags.html   # 厨余垃圾袋产品页面
├── pet-waste-bags.html         # 宠物垃圾袋产品页面（待创建）
├── grocery-bags.html           # 购物袋产品页面（待创建）
├── assets/                     # 静态资源目录
│   ├── css/                    # 样式文件
│   │   ├── bootstrap.min.css   # Bootstrap框架（需要下载）
│   │   ├── font-awesome.min.css # Font Awesome图标（需要下载）
│   │   ├── animate.css         # 动画库（需要下载）
│   │   ├── owl.carousel.min.css # 轮播图插件（需要下载）
│   │   └── style.css           # 自定义样式
│   ├── js/                     # JavaScript文件
│   │   ├── jquery.min.js       # jQuery库（需要下载）
│   │   ├── bootstrap.bundle.min.js # Bootstrap JS（需要下载）
│   │   ├── owl.carousel.min.js # 轮播图插件（需要下载）
│   │   ├── wow.min.js          # 动画库（需要下载）
│   │   └── main.js             # 自定义脚本
│   └── images/                 # 图片资源
│       ├── logo.png            # 公司Logo
│       ├── slider/             # 轮播图片
│       ├── products/           # 产品图片
│       ├── factory/            # 工厂图片
│       ├── services/           # 服务图片
│       ├── certifications/     # 认证图片
│       ├── contact/            # 联系页面图片
│       └── icons/              # 图标文件
└── README.md                   # 项目说明文件
```

## 页面功能

### 主页 (index.html)
- 响应式导航菜单
- 轮播图展示主要产品
- 产品展示区域
- 工厂实景展示
- 服务介绍
- 页脚联系信息

### 公司信息页面 (about.html)
- 公司介绍
- 生产流程展示
- 资质认证
- 工厂实景画廊

### 联系我们页面 (contact.html)
- 联系信息展示
- 联系表单
- 社交媒体链接
- 地图嵌入

### 产品页面
- 产品详细介绍
- 产品特点列表
- 生产工艺展示
- 相关产品推荐
- 联系咨询功能

## 技术特性

### 响应式设计
- 使用Bootstrap 5框架
- 适配桌面、平板、手机等设备
- 灵活的网格布局系统

### 交互功能
- Owl Carousel轮播图
- WOW.js滚动动画
- 图片灯箱效果
- 平滑滚动
- 返回顶部按钮

### 性能优化
- 图片懒加载
- CSS和JS文件压缩
- 优化的图片格式
- 缓存策略

## 安装和部署

### 1. 下载依赖文件
需要下载以下第三方库文件到对应目录：

**CSS文件 (assets/css/):**
- bootstrap.min.css
- font-awesome.min.css
- animate.css
- owl.carousel.min.css

**JavaScript文件 (assets/js/):**
- jquery.min.js
- bootstrap.bundle.min.js
- owl.carousel.min.js
- wow.min.js

### 2. 复制图片资源
从原WordPress网站的_files目录复制图片到assets/images/对应子目录：

**从 YK-EcoPack - Your Packs Factory_files/ 复制：**
- cropped-（长款）YK-logo.png → assets/images/logo.png
- 3.-Grocery-Bags-Compostable-1.jpg → assets/images/slider/grocery-bags.jpg
- Kitchen-Garbage-Bags-Compostable.jpg → assets/images/slider/kitchen-garbage-bags.jpg
- Pet-Waste-Bags.jpg → assets/images/slider/pet-waste-bags.jpg
- Grocery-Bags-Small-Image.jpg → assets/images/products/grocery-bags-small.jpg
- Kitchen-Garbage-Bags-Small-Image.jpg → assets/images/products/kitchen-garbage-bags-small.jpg
- Pet-Waste-Bags-Small-Iamge.jpg → assets/images/products/pet-waste-bags-small.jpg
- 工厂相关图片 → assets/images/factory/
- package-service.png → assets/images/services/

**从其他_files目录复制相应图片到对应位置**

### 3. 部署
将整个static-website目录上传到Web服务器即可。

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- IE 11+（部分功能可能受限）

## 维护说明

### 添加新产品
1. 复制kitchen-garbage-bags.html作为模板
2. 修改页面内容和图片路径
3. 更新导航菜单链接
4. 添加相应的产品图片

### 修改样式
主要样式文件为assets/css/style.css，包含：
- 基础样式重置
- 响应式布局
- 组件样式
- 动画效果
- 媒体查询

### 更新内容
- 联系信息：修改各页面footer部分
- 公司信息：编辑about.html
- 产品信息：编辑对应产品页面

## 注意事项

1. 确保所有图片路径正确
2. 测试所有链接和表单功能
3. 验证响应式设计在不同设备上的表现
4. 检查页面加载速度
5. 确保SEO优化（meta标签、alt属性等）

## 联系信息

如有问题，请联系：
- 电话：+86 150 1527 1728
- 邮箱：<EMAIL>
- 地址：中国广东省东莞市高埗镇北王路高埗段171号2号楼
