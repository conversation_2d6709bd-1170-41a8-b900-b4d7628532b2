// YK-EcoPack Main JavaScript

$(document).ready(function() {

    // Initialize Hero Carousel with error handling
    function initOwlCarousel() {
        if (typeof $.fn.owlCarousel === 'undefined') {
            console.warn('Owl Carousel plugin not loaded, retrying in 100ms...');
            setTimeout(initOwlCarousel, 100);
            return;
        }

        var $carousel = $('#hero-carousel');
        if ($carousel.length > 0) {
            console.log('Initializing Owl Carousel...');

            // Destroy existing carousel if any
            if ($carousel.hasClass('owl-loaded')) {
                $carousel.trigger('destroy.owl.carousel');
                $carousel.removeClass('owl-loaded owl-drag');
            }

            $carousel.owlCarousel({
                items: 1,
                loop: true,
                autoplay: true,
                autoplayTimeout: 5000,
                autoplayHoverPause: true,
                nav: true,
                dots: true,
                navText: ['<i class="fa fa-chevron-left"></i>', '<i class="fa fa-chevron-right"></i>'],
                responsive: {
                    0: {
                        nav: false
                    },
                    768: {
                        nav: true
                    }
                }
            });
            console.log('Owl Carousel initialized successfully');
        } else {
            console.warn('Hero carousel element not found');
        }
    }

    // Expose function globally for CDN fallback
    window.initOwlCarousel = initOwlCarousel;

    // Try to initialize carousel
    initOwlCarousel();
    
    // Smooth scrolling for anchor links (exclude language switcher)
    $('a[href^="#"]:not(.language-switcher a)').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if (target.length) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 1000);
        }
    });
    
    // Back to top button
    $(window).scroll(function() {
        if ($(this).scrollTop() > 100) {
            $('#back-to-top').fadeIn();
        } else {
            $('#back-to-top').fadeOut();
        }
    });
    
    $('#back-to-top').click(function(e) {
        e.preventDefault();
        $('html, body').animate({
            scrollTop: 0
        }, 800);
    });
    
    // Navbar scroll effect
    $(window).scroll(function() {
        if ($(this).scrollTop() > 50) {
            $('.header').addClass('scrolled');
        } else {
            $('.header').removeClass('scrolled');
        }
    });
    
    // Initialize WOW.js for animations
    if (typeof WOW !== 'undefined') {
        new WOW().init();
    }
    
    // Mobile menu toggle
    $('.navbar-toggler').click(function() {
        $(this).toggleClass('active');
    });
    
    // Close mobile menu when clicking on a link
    $('.navbar-nav .nav-link').click(function() {
        if ($(window).width() < 992) {
            $('.navbar-collapse').collapse('hide');
            $('.navbar-toggler').removeClass('active');
        }
    });
    
    // Product cards hover effect
    $('.product-card').hover(
        function() {
            $(this).find('.product-image img').css('transform', 'scale(1.1)');
        },
        function() {
            $(this).find('.product-image img').css('transform', 'scale(1)');
        }
    );
    
    // Gallery lightbox effect (if needed)
    $('.gallery-item img').click(function() {
        var src = $(this).attr('src');
        var alt = $(this).attr('alt');
        
        // Create lightbox modal
        var lightbox = $('<div class="lightbox-overlay">' +
            '<div class="lightbox-content">' +
            '<img src="' + src + '" alt="' + alt + '">' +
            '<button class="lightbox-close">&times;</button>' +
            '</div>' +
            '</div>');
        
        $('body').append(lightbox);
        lightbox.fadeIn();
        
        // Close lightbox
        lightbox.click(function(e) {
            if (e.target === this || $(e.target).hasClass('lightbox-close')) {
                lightbox.fadeOut(function() {
                    lightbox.remove();
                });
            }
        });
    });
    
    // Form validation (if contact forms are added)
    $('form').submit(function(e) {
        var isValid = true;
        
        $(this).find('input[required], textarea[required]').each(function() {
            if ($(this).val().trim() === '') {
                $(this).addClass('error');
                isValid = false;
            } else {
                $(this).removeClass('error');
            }
        });
        
        // Email validation
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        $(this).find('input[type="email"]').each(function() {
            if ($(this).val() && !emailRegex.test($(this).val())) {
                $(this).addClass('error');
                isValid = false;
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields and ensure the email format is correct.');
        }
    });
    
    // Lazy loading for images
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }
    
    // Scroll animations
    $(window).scroll(function() {
        $('.animate-fade-in').each(function() {
            var elementTop = $(this).offset().top;
            var elementBottom = elementTop + $(this).outerHeight();
            var viewportTop = $(window).scrollTop();
            var viewportBottom = viewportTop + $(window).height();
            
            if (elementBottom > viewportTop && elementTop < viewportBottom) {
                $(this).addClass('animated');
            }
        });
    });
    
    // Counter animation (if needed)
    function animateCounter(element, target) {
        var current = 0;
        var increment = target / 100;
        var timer = setInterval(function() {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            element.text(Math.floor(current));
        }, 20);
    }
    
    // Initialize counters when they come into view
    $('.counter').each(function() {
        var $this = $(this);
        var target = parseInt($this.data('target'));
        
        $(window).scroll(function() {
            var elementTop = $this.offset().top;
            var viewportBottom = $(window).scrollTop() + $(window).height();
            
            if (elementTop < viewportBottom && !$this.hasClass('animated')) {
                $this.addClass('animated');
                animateCounter($this, target);
            }
        });
    });
    
    // Preloader (if needed)
    $(window).on('load', function() {
        $('.preloader').fadeOut('slow');
    });
    
    // Language switcher functionality - Enhanced version
    function initLanguageSwitcher() {
        console.log('Initializing language switcher...');

        // Check if elements exist
        var switchers = $('.language-switcher a');
        console.log('Found', switchers.length, 'language switcher buttons');

        if (switchers.length === 0) {
            console.warn('No language switcher buttons found!');
            return;
        }

        // Remove any existing handlers to avoid duplicates
        switchers.off('click.languageSwitch');

        // Add click handler
        switchers.on('click.languageSwitch', function(e) {
            e.preventDefault();
            var lang = $(this).text().trim();

            console.log('Language switcher clicked:', lang);

            // Get current page name
            var currentPage = window.location.pathname.split('/').pop() || 'index.html';
            console.log('Current page:', currentPage);

            // Determine base path more reliably
            var currentPath = window.location.pathname;
            var basePath = '';

            if (currentPath.includes('/en_website/')) {
                basePath = currentPath.substring(0, currentPath.lastIndexOf('/en_website/'));
            } else if (currentPath.includes('/cn_website/')) {
                basePath = currentPath.substring(0, currentPath.lastIndexOf('/cn_website/'));
            } else {
                // We're probably in the root directory or accessing directly
                basePath = window.location.origin + window.location.pathname.substring(0, window.location.pathname.lastIndexOf('/'));
                if (basePath === window.location.origin) {
                    basePath = '.';
                }
            }

            console.log('Base path:', basePath);

            // Language switching logic
            if (lang === '中文版') {
                // Switch to Chinese version
                var chineseUrl = basePath + '/cn_website/' + currentPage;
                console.log('Redirecting to Chinese:', chineseUrl);

                // Add visual feedback before redirect
                $(this).addClass('active').siblings().removeClass('active');

                // Small delay for visual feedback
                setTimeout(function() {
                    window.location.href = chineseUrl;
                }, 200);

            } else if (lang === 'EN') {
                // Already on English version, add visual feedback
                console.log('Already on English version, showing feedback');
                $(this).addClass('active').siblings().removeClass('active');
                setTimeout(function() {
                    $('.language-switcher a').removeClass('active');
                }, 1500);
            }
        });

        console.log('Language switcher initialized successfully');
    }

    // Initialize language switcher
    initLanguageSwitcher();

    // Also try to initialize after a short delay in case DOM isn't fully ready
    setTimeout(initLanguageSwitcher, 100);
    
    // Social media sharing (if needed)
    $('.share-btn').click(function(e) {
        e.preventDefault();
        var url = encodeURIComponent(window.location.href);
        var title = encodeURIComponent(document.title);
        var platform = $(this).data('platform');
        var shareUrl = '';
        
        switch(platform) {
            case 'facebook':
                shareUrl = 'https://www.facebook.com/sharer/sharer.php?u=' + url;
                break;
            case 'twitter':
                shareUrl = 'https://twitter.com/intent/tweet?url=' + url + '&text=' + title;
                break;
            case 'linkedin':
                shareUrl = 'https://www.linkedin.com/sharing/share-offsite/?url=' + url;
                break;
        }
        
        if (shareUrl) {
            window.open(shareUrl, '_blank', 'width=600,height=400');
        }
    });
    
});

// Additional utility functions
function debounce(func, wait, immediate) {
    var timeout;
    return function() {
        var context = this, args = arguments;
        var later = function() {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };
        var callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(context, args);
    };
}

// Optimized scroll handler
var optimizedScroll = debounce(function() {
    // Scroll-based animations and effects
    var scrollTop = $(window).scrollTop();
    
    // Parallax effect for hero section
    $('.hero-slider').css('transform', 'translateY(' + scrollTop * 0.5 + 'px)');
    
    // Update navigation active state based on scroll position
    var sections = $('section[id]');
    sections.each(function() {
        var section = $(this);
        var sectionTop = section.offset().top - 150;
        var sectionBottom = sectionTop + section.outerHeight();
        
        if (scrollTop >= sectionTop && scrollTop < sectionBottom) {
            var sectionId = section.attr('id');
            $('.navbar-nav .nav-link').removeClass('active');
            $('.navbar-nav .nav-link[href="#' + sectionId + '"]').addClass('active');
        }
    });
}, 10);

$(window).scroll(optimizedScroll);
