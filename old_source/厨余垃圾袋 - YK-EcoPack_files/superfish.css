/*** arrows (for all except IE7) **/
.sf-arrows .sf-with-ul {
	padding-right: 1.5em;
	*padding-right: 1em; /* no CSS arrows for IE7 (lack pseudo-elements) */
}
/* styling for both css and generated arrows */
.sf-arrows .sf-with-ul:after {
	/*content: '\f107';
	font-family: FontAwesome;
	position: absolute;
	right: 2em;
	height: 0;
	width: 0;*/
}
.sf-arrows > li > .sf-with-ul:focus:after,
.sf-arrows > li:hover > .sf-with-ul:after,
.sf-arrows > .sfHover > .sf-with-ul:after {
	border-top-color: white; /* IE8 fallback colour */
}
/* styling for right-facing arrows */
.sf-arrows ul .sf-with-ul:after {
	content: '\f105';
}
.sf-arrows ul li > .sf-with-ul:focus:after,
.sf-arrows ul li:hover > .sf-with-ul:after,
.sf-arrows ul .sfHover > .sf-with-ul:after {
	border-left-color: white;
}
.sf-arrows .sf-with-ul:before{
content: '\f107';
    position: absolute;
    right: 4px;
    top: 5%;
	font-family: FontAwesome;
    font-weight: 400;
    font-size: 14px;
/*    -webkit-transform: skew(25deg);
    -ms-transform: skew(25deg);
    transform: skew(25deg);*/
}