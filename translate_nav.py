#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to translate navigation menus in HTML files from Chinese to English
"""

import os
import re

# Translation mappings
translations = {
    # Navigation items
    '主页': 'Home',
    '公司': 'About Us', 
    '产品': 'Products',
    '服务': 'Services',
    '联系方式': 'Contact',
    '联系我们': 'Contact Us',
    
    # Product items
    '宠物垃圾袋': 'Pet Waste Bags',
    '厨余垃圾袋': 'Kitchen Garbage Bags', 
    '购物袋': 'Grocery Bags',
    
    # Service items
    '品牌商标设计': 'Brand Design',
    '产品设计': 'Product Design',
    '印刷图案设计': 'Printing Design',
    '包装设计': 'Package Design',
    
    # Page titles
    '公司信息': 'About Us',
    '我们的产品': 'Our Products',
    '我们的服务': 'Our Services',
    '工厂实景': 'Factory Gallery',
    '关于我们公司': 'About Our Company',
    '公司照片': 'Company Photos',
    '公司简介': 'Company Profile',
    '我们的使命': 'Our Mission',
    '我们的愿景': 'Our Vision',
    '我们的优势': 'Our Advantages',
    '生产能力': 'Production Capacity',
    '研发能力': 'R&D Capability',
    '品质控制': 'Quality Control',
    
    # Factory terms
    '吹膜环节': 'Film Blowing Process',
    '温度控制器': 'Temperature Controller',
    '袋子印刷车间': 'Bag Printing Workshop',
    '可降解吹膜收卷': 'Biodegradable Film Rolling',
    '仓库': 'Warehouse',
    '公司大门': 'Company Gate',
    '工厂介绍': 'Factory Introduction',
    
    # Common terms
    '查看详情': 'View Details',
    '关于我们': 'About Us',
    '超过15年袋子生产及出口经验': 'Over 15 Years of Bag Production and Export Experience',
    '中国广东省东莞市高埗镇北王路高埗段171号2号楼': 'Building 2, No. 171, Gaobei Section, Beiwang Road, Gaobu Town, Dongguan City, Guangdong Province, China',
    '公司地址：': 'Company Address:',
}

def translate_file(filepath):
    """Translate Chinese text in HTML file to English"""
    print(f"Processing {filepath}...")
    
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # Apply translations
    for chinese, english in translations.items():
        # Use word boundary matching to avoid partial replacements
        pattern = re.escape(chinese)
        content = re.sub(pattern, english, content)
    
    # Only write if content changed
    if content != original_content:
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  ✓ Updated {filepath}")
    else:
        print(f"  - No changes needed for {filepath}")

def main():
    """Main function to process all HTML files"""
    html_dir = 'en_website'
    
    if not os.path.exists(html_dir):
        print(f"Directory {html_dir} not found!")
        return
    
    # Get all HTML files
    html_files = [f for f in os.listdir(html_dir) if f.endswith('.html')]
    
    print(f"Found {len(html_files)} HTML files to process:")
    for file in html_files:
        print(f"  - {file}")
    
    print("\nStarting translation...")
    
    for html_file in html_files:
        filepath = os.path.join(html_dir, html_file)
        translate_file(filepath)
    
    print("\nTranslation completed!")

if __name__ == '__main__':
    main()
