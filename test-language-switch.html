<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语言切换测试 - Language Switch Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .test-links {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .test-links a {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .test-links a:hover {
            background: #0056b3;
        }
        .instructions {
            background: #e7f3ff;
            padding: 15px;
            border-left: 4px solid #007bff;
            margin: 20px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>YK-EcoPack 语言切换功能测试</h1>
    
    <div class="instructions">
        <h3>测试说明 / Test Instructions:</h3>
        <p><strong>中文:</strong> 点击下面的链接测试语言切换功能。每个页面的语言切换按钮应该能正确跳转到对应语言版本的相同页面。</p>
        <p><strong>English:</strong> Click the links below to test the language switching functionality. The language switcher buttons on each page should correctly navigate to the corresponding page in the other language.</p>
    </div>

    <div class="test-section">
        <h2>中文版网站测试 (Chinese Website Test)</h2>
        <p>测试中文版网站的语言切换功能：</p>
        <div class="test-links">
            <a href="cn_website/index.html" target="_blank">首页 (Home)</a>
            <a href="cn_website/about.html" target="_blank">关于我们 (About)</a>
            <a href="cn_website/contact.html" target="_blank">联系我们 (Contact)</a>
            <a href="cn_website/pet-waste-bags.html" target="_blank">宠物垃圾袋</a>
            <a href="cn_website/grocery-bags.html" target="_blank">购物袋</a>
        </div>
    </div>

    <div class="test-section">
        <h2>英文版网站测试 (English Website Test)</h2>
        <p>测试英文版网站的语言切换功能：</p>
        <div class="test-links">
            <a href="en_website/index.html" target="_blank">Home (首页)</a>
            <a href="en_website/about.html" target="_blank">About Us (关于我们)</a>
            <a href="en_website/contact.html" target="_blank">Contact (联系我们)</a>
            <a href="en_website/pet-waste-bags.html" target="_blank">Pet Waste Bags</a>
            <a href="en_website/grocery-bags.html" target="_blank">Grocery Bags</a>
        </div>
    </div>

    <div class="test-section">
        <h2>功能实现状态 (Implementation Status)</h2>
        <div class="status success">
            ✅ <strong>已完成:</strong> 语言切换JavaScript逻辑已实现
        </div>
        <div class="status success">
            ✅ <strong>已完成:</strong> 中文版和英文版网站内容翻译
        </div>
        <div class="status success">
            ✅ <strong>已完成:</strong> 导航菜单和页面内容本地化
        </div>
        <div class="status info">
            ℹ️ <strong>说明:</strong> 语言切换按钮保持原样，符合用户要求
        </div>
    </div>

    <div class="test-section">
        <h2>测试步骤 (Test Steps)</h2>
        <ol>
            <li>点击上方任意链接打开网站页面</li>
            <li>在页面右上角找到语言切换按钮: 
                <code style="background:#f8f9fa;padding:2px 6px;border-radius:3px;">中文版 | EN</code>
            </li>
            <li>点击对应的语言按钮</li>
            <li>验证是否正确跳转到相同页面的另一语言版本</li>
            <li>在新页面上再次测试语言切换功能</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>预期行为 (Expected Behavior)</h2>
        <ul>
            <li><strong>从中文版点击 "EN":</strong> 跳转到英文版的相同页面</li>
            <li><strong>从英文版点击 "中文版":</strong> 跳转到中文版的相同页面</li>
            <li><strong>点击当前语言按钮:</strong> 显示视觉反馈，不跳转</li>
            <li><strong>URL结构:</strong> 保持 /cn_website/ 和 /en_website/ 的目录结构</li>
        </ul>
    </div>

    <div class="instructions">
        <h3>技术实现细节 (Technical Implementation):</h3>
        <p><strong>JavaScript逻辑:</strong> 修改了 main.js 文件中的语言切换功能</p>
        <p><strong>路径解析:</strong> 自动检测当前路径并构建正确的目标URL</p>
        <p><strong>用户体验:</strong> 添加了视觉反馈，点击当前语言时显示激活状态</p>
    </div>
</body>
</html>
