<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Owl Carousel Test</title>
    <link rel="stylesheet" href="cn_website/assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="cn_website/assets/css/owl.carousel.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .hero-slide img {
            width: 100%;
            height: 300px;
            object-fit: cover;
        }
        #debug-log {
            background: #000;
            color: #0f0;
            padding: 15px;
            font-family: monospace;
            height: 200px;
            overflow-y: auto;
            margin: 20px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>🎠 Owl Carousel 加载测试</h1>
    
    <div class="test-section">
        <h3>📊 加载状态检查</h3>
        <div id="jquery-status" class="status">检查中...</div>
        <div id="owl-status" class="status">检查中...</div>
        <div id="files-status" class="status">检查中...</div>
    </div>
    
    <div class="test-section">
        <h3>🎠 测试轮播图</h3>
        <div class="owl-carousel owl-theme" id="test-carousel">
            <div class="item">
                <div class="hero-slide">
                    <img src="cn_website/assets/images/slider/grocery-bags.jpg" alt="Test 1" class="img-fluid">
                    <div style="text-align: center; padding: 10px;">
                        <h4>测试图片 1</h4>
                    </div>
                </div>
            </div>
            <div class="item">
                <div class="hero-slide">
                    <img src="cn_website/assets/images/slider/kitchen-garbage-bags.jpg" alt="Test 2" class="img-fluid">
                    <div style="text-align: center; padding: 10px;">
                        <h4>测试图片 2</h4>
                    </div>
                </div>
            </div>
            <div class="item">
                <div class="hero-slide">
                    <img src="cn_website/assets/images/slider/pet-waste-bags.jpg" alt="Test 3" class="img-fluid">
                    <div style="text-align: center; padding: 10px;">
                        <h4>测试图片 3</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h3>🔧 调试信息</h3>
        <div id="debug-log">等待加载...</div>
        <button onclick="clearLog()">清空日志</button>
        <button onclick="testCarousel()">重新测试轮播</button>
    </div>

    <!-- JavaScript Files in correct order -->
    <script src="cn_website/assets/js/jquery.min.js"></script>
    <script src="cn_website/assets/js/bootstrap.bundle.min.js"></script>
    <script src="cn_website/assets/js/owl.carousel.min.js"></script>
    
    <script>
        function log(message) {
            const debugLog = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            debugLog.innerHTML += `[${timestamp}] ${message}\n`;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            document.getElementById('debug-log').innerHTML = '';
        }
        
        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = 'status ' + type;
        }
        
        function testCarousel() {
            log('🔄 Testing carousel initialization...');
            
            if (typeof $ === 'undefined') {
                log('❌ jQuery not loaded');
                return;
            }
            
            if (typeof $.fn.owlCarousel === 'undefined') {
                log('❌ Owl Carousel plugin not loaded');
                return;
            }
            
            const $carousel = $('#test-carousel');
            if ($carousel.length === 0) {
                log('❌ Carousel element not found');
                return;
            }
            
            try {
                // Destroy existing carousel if any
                if ($carousel.hasClass('owl-loaded')) {
                    $carousel.trigger('destroy.owl.carousel');
                    log('🗑️ Destroyed existing carousel');
                }
                
                // Initialize carousel
                $carousel.owlCarousel({
                    items: 1,
                    loop: true,
                    autoplay: true,
                    autoplayTimeout: 3000,
                    nav: true,
                    dots: true,
                    navText: ['‹', '›']
                });
                
                log('✅ Carousel initialized successfully');
                updateStatus('owl-status', '✅ Owl Carousel 工作正常', 'success');
                
            } catch (error) {
                log('❌ Error initializing carousel: ' + error.message);
                updateStatus('owl-status', '❌ Owl Carousel 初始化失败: ' + error.message, 'error');
            }
        }
        
        // Check loading status
        $(document).ready(function() {
            log('📄 DOM ready');
            
            // Check jQuery
            if (typeof $ !== 'undefined') {
                log('✅ jQuery loaded: ' + $.fn.jquery);
                updateStatus('jquery-status', '✅ jQuery 已加载 (版本: ' + $.fn.jquery + ')', 'success');
            } else {
                log('❌ jQuery not loaded');
                updateStatus('jquery-status', '❌ jQuery 未加载', 'error');
                return;
            }
            
            // Check Owl Carousel
            if (typeof $.fn.owlCarousel !== 'undefined') {
                log('✅ Owl Carousel plugin loaded');
                updateStatus('owl-status', '✅ Owl Carousel 插件已加载', 'success');
            } else {
                log('❌ Owl Carousel plugin not loaded');
                updateStatus('owl-status', '❌ Owl Carousel 插件未加载', 'error');
                
                // Try to load it manually
                log('🔄 Attempting to reload Owl Carousel...');
                setTimeout(function() {
                    if (typeof $.fn.owlCarousel !== 'undefined') {
                        log('✅ Owl Carousel loaded after delay');
                        updateStatus('owl-status', '✅ Owl Carousel 延迟加载成功', 'success');
                        testCarousel();
                    } else {
                        log('❌ Owl Carousel still not available');
                        updateStatus('owl-status', '❌ Owl Carousel 延迟加载失败', 'error');
                    }
                }, 500);
                return;
            }
            
            // Check file accessibility
            updateStatus('files-status', '✅ 所有文件加载正常', 'success');
            
            // Initialize carousel
            testCarousel();
        });
        
        // Handle script loading errors
        window.addEventListener('error', function(e) {
            if (e.filename && e.filename.includes('owl.carousel')) {
                log('❌ Error loading Owl Carousel: ' + e.message);
                updateStatus('owl-status', '❌ Owl Carousel 文件加载错误', 'error');
            }
        });
    </script>
</body>
</html>
