#!/bin/bash

# YK-EcoPack Language Switch Test Server
# This script starts a simple HTTP server to test the language switching functionality

echo "🚀 Starting YK-EcoPack Language Switch Test Server..."
echo "📁 Current directory: $(pwd)"
echo ""

# Check if Python is available
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    echo "❌ Error: Python is not installed or not in PATH"
    exit 1
fi

echo "🐍 Using Python: $PYTHON_CMD"

# Find an available port
PORT=8000
while netstat -an | grep -q ":$PORT "; do
    PORT=$((PORT + 1))
done

echo "🌐 Server will start on port: $PORT"
echo ""
echo "📋 Test URLs:"
echo "   Main Test Page:     http://localhost:$PORT/test-language-switch.html"
echo "   Chinese Website:    http://localhost:$PORT/cn_website/index.html"
echo "   English Website:    http://localhost:$PORT/en_website/index.html"
echo ""
echo "🔧 Testing Instructions:"
echo "   1. Open the main test page in your browser"
echo "   2. Click on any website link to open a page"
echo "   3. Look for the language switcher in the top-right corner"
echo "   4. Click '中文版' or 'EN' to test language switching"
echo "   5. Verify that you're redirected to the correct language version"
echo ""
echo "⚠️  Note: Make sure both cn_website and en_website directories exist"
echo "💡 Tip: Use Ctrl+C to stop the server"
echo ""
echo "🎯 Starting server..."

# Start the HTTP server
$PYTHON_CMD -m http.server $PORT

echo ""
echo "👋 Server stopped. Thank you for testing!"
