/**
 * Enhanced Language Switcher for YK-EcoPack Website
 * Supports switching between Chinese (cn_website) and English (en_website) versions
 */

(function() {
    'use strict';
    
    // Configuration
    const CONFIG = {
        chineseDir: 'cn_website',
        englishDir: 'en_website',
        chineseLabel: '中文版',
        englishLabel: 'EN',
        feedbackDuration: 1000
    };
    
    /**
     * Get the current language based on the URL path
     * @returns {string} 'cn' or 'en'
     */
    function getCurrentLanguage() {
        const path = window.location.pathname;
        if (path.includes('/' + CONFIG.chineseDir + '/')) {
            return 'cn';
        } else if (path.includes('/' + CONFIG.englishDir + '/')) {
            return 'en';
        }
        // Default to Chinese if unclear
        return 'cn';
    }
    
    /**
     * Get the current page filename
     * @returns {string} The current page filename
     */
    function getCurrentPage() {
        const path = window.location.pathname;
        const filename = path.split('/').pop();
        return filename || 'index.html';
    }
    
    /**
     * Build the target URL for language switching
     * @param {string} targetLang - Target language ('cn' or 'en')
     * @returns {string} The target URL
     */
    function buildTargetUrl(targetLang) {
        const currentPage = getCurrentPage();
        const currentPath = window.location.pathname;
        const currentLang = getCurrentLanguage();
        
        // Get the base path (everything before the language directory)
        let basePath = '';
        if (currentLang === 'cn') {
            basePath = currentPath.substring(0, currentPath.lastIndexOf('/' + CONFIG.chineseDir + '/'));
        } else {
            basePath = currentPath.substring(0, currentPath.lastIndexOf('/' + CONFIG.englishDir + '/'));
        }
        
        // Handle case where we can't find the language directory (e.g., direct file access)
        if (basePath === currentPath) {
            basePath = window.location.origin;
        }
        
        // Build target URL
        const targetDir = targetLang === 'cn' ? CONFIG.chineseDir : CONFIG.englishDir;
        return basePath + '/' + targetDir + '/' + currentPage;
    }
    
    /**
     * Show visual feedback when clicking the current language button
     * @param {jQuery} button - The button element
     */
    function showFeedback(button) {
        button.addClass('active').siblings().removeClass('active');
        setTimeout(function() {
            button.removeClass('active');
        }, CONFIG.feedbackDuration);
    }
    
    /**
     * Handle language switch click
     * @param {Event} e - Click event
     */
    function handleLanguageSwitch(e) {
        e.preventDefault();
        
        const button = $(this);
        const clickedLang = button.text().trim();
        const currentLang = getCurrentLanguage();
        
        // Determine target language
        let targetLang = null;
        if (clickedLang === CONFIG.englishLabel) {
            targetLang = 'en';
        } else if (clickedLang === CONFIG.chineseLabel) {
            targetLang = 'cn';
        }
        
        if (!targetLang) {
            console.warn('Unknown language button clicked:', clickedLang);
            return;
        }
        
        // Check if we're already on the target language
        if (currentLang === targetLang) {
            showFeedback(button);
            return;
        }
        
        // Switch to target language
        const targetUrl = buildTargetUrl(targetLang);
        
        // Add loading state
        button.addClass('loading').prop('disabled', true);
        
        // Navigate to target URL
        window.location.href = targetUrl;
    }
    
    /**
     * Initialize the language switcher
     */
    function initLanguageSwitcher() {
        // Wait for DOM to be ready
        $(document).ready(function() {
            // Bind click event to language switcher buttons
            $('.language-switcher a').off('click.languageSwitch').on('click.languageSwitch', handleLanguageSwitch);
            
            // Add visual indicator for current language
            const currentLang = getCurrentLanguage();
            $('.language-switcher a').each(function() {
                const button = $(this);
                const buttonText = button.text().trim();
                
                if ((currentLang === 'cn' && buttonText === CONFIG.chineseLabel) ||
                    (currentLang === 'en' && buttonText === CONFIG.englishLabel)) {
                    button.addClass('current-lang');
                }
            });
            
            console.log('Language switcher initialized. Current language:', currentLang);
        });
    }
    
    // Initialize when script loads
    initLanguageSwitcher();
    
    // Expose for debugging
    window.YKLanguageSwitcher = {
        getCurrentLanguage: getCurrentLanguage,
        getCurrentPage: getCurrentPage,
        buildTargetUrl: buildTargetUrl,
        config: CONFIG
    };
    
})();
