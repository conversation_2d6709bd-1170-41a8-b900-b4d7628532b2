<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Language Switch Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .language-switcher {
            margin: 20px 0;
            text-align: center;
        }
        .btn {
            display: inline-block;
            padding: 8px 16px;
            margin: 0 5px;
            text-decoration: none;
            border: 1px solid #007bff;
            color: #007bff;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #007bff;
            color: white;
        }
        .btn.active {
            background: #28a745 !important;
            color: white !important;
            border-color: #28a745 !important;
            transform: scale(1.05);
        }
        .test-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .links {
            margin: 20px 0;
        }
        .links a {
            display: inline-block;
            margin: 5px 10px;
            padding: 10px 15px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .links a:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>语言切换功能测试</h1>
    
    <div class="test-info">
        <h3>当前页面信息:</h3>
        <p><strong>URL:</strong> <span id="current-url"></span></p>
        <p><strong>路径:</strong> <span id="current-path"></span></p>
        <p><strong>页面:</strong> <span id="current-page"></span></p>
    </div>
    
    <div class="language-switcher Rqst-btn">
        <a href="#" class="btn btn-outline-primary btn-sm">中文版</a>
        <a href="#" class="btn btn-outline-primary btn-sm">EN</a>
    </div>
    
    <div class="test-info">
        <h3>测试链接:</h3>
        <div class="links">
            <a href="cn_website/index.html">中文首页</a>
            <a href="en_website/index.html">英文首页</a>
            <a href="cn_website/about.html">中文关于</a>
            <a href="en_website/about.html">英文关于</a>
        </div>
    </div>
    
    <div id="debug-log" style="background: #000; color: #0f0; padding: 10px; font-family: monospace; height: 200px; overflow-y: auto; margin: 20px 0;"></div>

    <!-- Load jQuery -->
    <script src="en_website/assets/js/jquery.min.js"></script>
    
    <script>
        function log(message) {
            const debugLog = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            debugLog.innerHTML += `[${timestamp}] ${message}\n`;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(message);
        }
        
        $(document).ready(function() {
            log('jQuery loaded and DOM ready');
            
            // Update page info
            $('#current-url').text(window.location.href);
            $('#current-path').text(window.location.pathname);
            $('#current-page').text(window.location.pathname.split('/').pop() || 'test-simple.html');
            
            // Language switcher functionality
            $('.language-switcher a').click(function(e) {
                e.preventDefault();
                var lang = $(this).text().trim();
                
                log('Language switcher clicked: ' + lang);
                
                // Get current page name
                var currentPage = window.location.pathname.split('/').pop() || 'index.html';
                log('Current page: ' + currentPage);
                
                // Get current path and determine base path
                var currentPath = window.location.pathname;
                var basePath = '';
                
                if (currentPath.includes('/cn_website/')) {
                    basePath = currentPath.substring(0, currentPath.lastIndexOf('/cn_website/'));
                    log('Detected cn_website, base path: ' + basePath);
                } else if (currentPath.includes('/en_website/')) {
                    basePath = currentPath.substring(0, currentPath.lastIndexOf('/en_website/'));
                    log('Detected en_website, base path: ' + basePath);
                } else {
                    // We're in root directory
                    var pathParts = currentPath.split('/');
                    pathParts.pop(); // Remove filename
                    basePath = pathParts.join('/');
                    if (basePath === '') basePath = '.';
                    log('Root directory detected, base path: ' + basePath);
                }
                
                // Language switching logic
                if (lang === '中文版') {
                    var chineseUrl = basePath + '/cn_website/' + currentPage;
                    log('Would redirect to Chinese: ' + chineseUrl);
                    
                    // Show visual feedback first
                    $(this).addClass('active').siblings().removeClass('active');
                    
                    // Redirect after a short delay
                    setTimeout(function() {
                        window.location.href = chineseUrl;
                    }, 500);
                    
                } else if (lang === 'EN') {
                    var englishUrl = basePath + '/en_website/' + currentPage;
                    log('Would redirect to English: ' + englishUrl);
                    
                    // Show visual feedback first
                    $(this).addClass('active').siblings().removeClass('active');
                    
                    // Redirect after a short delay
                    setTimeout(function() {
                        window.location.href = englishUrl;
                    }, 500);
                }
            });
            
            log('Language switcher initialized');
        });
    </script>
</body>
</html>
