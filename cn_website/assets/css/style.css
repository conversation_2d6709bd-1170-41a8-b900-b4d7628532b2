/* YK-EcoPack Custom Styles */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
}

.container {
    max-width: 1300px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}


.header .Rqst-btn a {
    position: relative;
    overflow: hidden;
    display: inline-block;
    text-decoration: none;
    font-weight: 300;
    font-size: 16px;
    margin-right: 1px;
    padding: 10px;
    color: #fff;
    transition: all 0.5s;
    border-radius: 0px;
    cursor: pointer;
    text-align: center;
    z-index: 9;
    max-width: 12em;
    word-break: break-all;
    background-color: #c9593f;
    border: none;
    float: left;
}
.header .Rqst-btn a:after {
  position: absolute;
  content: "";
  width: 100%;
  height: 0;
  bottom: 0;
  left: 0;
  z-index: -1;
  border-radius: 0px;
   background-color: #000;
/*background-image: linear-gradient(315deg, #4dccc6 0%, #96e4df 74%);*/
  transition: all 0.3s ease;
}
.header .Rqst-btn a:hover {
  color: #c9593f;
}
.header .Rqst-btn a:hover:after {
  top: 0;
  height: 100%;
}
.header .Rqst-btn a:active {
  top: 2px;
}

.top-bar {
    background: #f8f9fa;
    padding: 10px 0;
}

.social-links a {
    color: #666;
    margin-right: 15px;
    font-size: 16px;
    transition: color 0.3s;
}

.social-links a:hover {
    color: #c9593f;
}

.main-header {
    padding: 15px 0;
}

.logo img {
    max-height: 60px;
}

.tagline {
    font-size: 14px;
    color: #666;
    margin: 0;
}

.navbar-nav .nav-link {
    color: #333;
    font-weight: 500;
    margin: 0 15px;
    transition: color 0.3s;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: #c9593f;
}

.language-switcher .btn {
    margin: 0 5px;
    font-size: 12px;
}

/* Hero Slider */
.hero-slider {
    margin-top: 150px;
    position: relative;
    z-index: 1;
    margin-bottom: 0;
}

.hero-slide {
    position: relative;
    overflow: hidden;
}

.hero-slider .item img {
    width: 100%;
    height: 710px;
    object-fit: cover;
}

.hero-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    background: rgba(0, 0, 0, 0.3);
}

.hero-text {
    color: white;
    padding: 40px 0;
}

.hero-text h1 {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-text h2 {
    font-size: 32px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #4CAF50;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-text p {
    font-size: 18px;
    margin-bottom: 30px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.hero-text .btn {
    padding: 15px 30px;
    font-size: 16px;
    font-weight: 600;
    text-transform: uppercase;
    border-radius: 30px;
}

/* Section Styles */
.section-title {
    margin-bottom: 50px;
}

.section-title h2 {
    font-size: 36px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
}

.border-line {
    width: 60px;
    height: 3px;
    background: #c9593f;
    margin: 0 auto;
}

/* Products Section */
.products-section {
    padding: 80px 0;
    position: relative;
    z-index: 2;
    background: #fff;
}

/*----------------------------------------
/* Blog Area (Product Cards)
/*----------------------------------------*/
.blog-area {
    position: relative;
    overflow: hidden;
}

.blog-area .blog-post {
    margin-bottom: 1.5em;
}

.blog-area li {
    list-style: none;
}

.blog-area .blog-single {
    padding: 1.2em 1em 1em 1em;
    position: relative;
    overflow: hidden;
    transition: all .5s;
    border: 1px solid #ddd;
    border-top: none;
    background: #fff;
}

.blog-area .inner-area-title {
    position: relative;
    font-size: 16px;
    font-weight: 600;
    word-break: break-word;
    padding: 0 0 0em;
    line-height: 30px;
    margin: 15px 0;
    color: #333;
}

.blog-area .inner-area-title:hover {
    color: #c9593f;
}

.blog-area .section-area-text {
    font-size: 16px;
    margin-bottom: 0;
    font-weight: 400;
    margin-top: 12px;
    color: #666;
    line-height: 1.6;
    min-height: 130px;
}

.blog-area .blog-thumbnail {
    position: relative;
    z-index: 0;
    overflow: hidden;
}

.blog-area .blog-thumbnail img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: all .5s ease;
}

.blog-area .blog-thumbnail:before,
.blog-area .blog-thumbnail:after {
    content: "";
    background-color: rgba(201, 89, 63, 0.3);
    height: 100%;
    width: 100%;
    opacity: 0.7;
    position: absolute;
    top: 0;
    left: -100%;
    z-index: 0;
    transition: all 0.4s ease-out 0.1s;
}

.blog-area .blog-post:hover .blog-thumbnail:after {
    opacity: 0;
    transform: scale(0.9, 0.7);
    left: 0;
    transition: all 0.3s ease-out 0s;
}

.blog-area .blog-post:hover .blog-thumbnail:before {
    left: 100%;
}

.blog-area .blog-post:hover .blog-thumbnail:after {
    opacity: 0.3;
    transform: scale(1);
}

/*=========button===========*/
.blog-area .btn5 a {
    position: relative;
    overflow: hidden;
    display: inline-block;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    padding: 8px 12px;
    color: #fff;
    transition: all 0.5s;
    border-radius: 0px;
    cursor: pointer;
    text-align: center;
    z-index: 9;
    word-break: break-all;
    background-color: #c9593f;
    border: none;
    margin-top: 1.5em;
}

.blog-area .btn5 a:after {
    position: absolute;
    content: "";
    width: 100%;
    height: 0;
    bottom: 0;
    left: 0;
    z-index: -1;
    border-radius: 0px;
    background-color: #000000;
    transition: all 0.3s ease;
}

.blog-area .btn5 a:hover {
    color: #fff;
    text-decoration: none;
}

.blog-area .btn5 a:hover:after {
    top: 0;
    height: 100%;
}

.blog-area .btn5 a:active {
    top: 2px;
}

.blog-area .btn5 a i {
    padding: 5px 10px;
    color: #000000;
    background: #FFFFFF;
    margin-left: 8px;
    font-size: 16px;
    font-weight: 300;
    display: inline-block;
    vertical-align: middle;
}

/* Product Price Styling */
.product-price {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #c9593f;
}

.product-price .price-label {
    font-size: 14px;
    color: #666;
    margin-right: 10px;
}

.product-price .price {
    font-size: 18px;
    font-weight: 600;
    color: #c9593f;
}

/* Utility Classes */
.pd-0 {
    padding: 0;
}

.clearfix::after {
    content: "";
    display: table;
    clear: both;
}

.btn-primary {
    background: #c9593f;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s;
}

.btn-primary:hover {
    background: #a84832;
    transform: translateY(-2px);
}

/* Gallery Area */
#gallery {
    position: relative;
    overflow: hidden;
    padding: 80px 0;
}

#gallery .section-title .border1 {
    display: block;
    position: relative;
    margin: 1em auto 0;
    content: " ";
    text-shadow: none;
    width: 140px;
    border-radius: 50px;
    border-style: solid;
    border-width: 6px;
    border-left: none;
    border-right: none;
    border-bottom: none;
    border-color: #c9593f;
}

#gallery .section-title h2 {
    font-size: 36px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
}

#gallery .htext {
    font-size: 16px;
    line-height: 24px;
}

#gallery .gallery-area-data h4 {
    font-size: 30px;
    margin: 0;
    font-weight: 600;
}

#gallery .gallery-area-data p {
    font-size: 16px;
    line-height: 24px;
}

#gallery .galleryus-single {
    margin-top: 2em;
}

/* Gallery Images */
#gallery .abtimg {
    display: block;
    margin-bottom: 30px;
}

#gallery .abtimginn {
    border-right: 4px solid #fff;
    margin-bottom: 20px;
}

#gallery .abou-img2 img,
#gallery .abou-img3 img {
    width: 100%;
    max-height: 242px;
    border-radius: 5px;
    object-fit: cover;
}

#gallery .abou-img img {
    width: 100%;
    height: 540px;
    border-radius: 5px;
    object-fit: cover;
}

#gallery .about-img-text {
    margin: 15px auto;
    text-align: center;
    font-size: 16px;
    color: #333;
}

#gallery .about-btn {
    text-align: center;
}

/* Gallery Button Styles */
#gallery .btn5 a {
    position: relative;
    overflow: hidden;
    display: inline-block;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    padding: 8px 12px;
    color: #fff;
    transition: all 0.5s;
    border-radius: 0px;
    cursor: pointer;
    text-align: center;
    z-index: 9;
    word-break: break-all;
    background-color: #c9593f;
    border: none;
    margin-top: 1.5em;
}

#gallery .btn5 a:after {
    position: absolute;
    content: "";
    width: 100%;
    height: 0;
    bottom: 0;
    left: 0;
    z-index: -1;
    border-radius: 0px;
    background-color: #4dccc6;
    transition: all 0.3s ease;
}

#gallery .btn5 a:hover {
    color: #fff;
    text-decoration: none;
}

#gallery .btn5 a:hover:after {
    top: 0;
    height: 100%;
    background-color: #000;
}

#gallery .btn5 a:active {
    top: 2px;
}

#gallery .btn5 a i {
    color: #000;
    padding: 5px 10px;
    background: #fff;
    margin-left: 8px;
    font-size: 20px;
    font-weight: 300;
}

/* Utility Classes for Gallery */
.img-responsive {
    max-width: 100%;
    height: auto;
}

.secondry-bg-img {
    background-size: cover;
    background-position: center;
}

.wow.animated {
    animation-duration: 1s;
    animation-fill-mode: both;
}

/*----------------------------------------*/
/*   Service Area
/*----------------------------------------*/
.service-area {
    position: relative;
    overflow: hidden;
    padding: 80px 0;
}

.service-area .single-service-bx {
    position: relative;
    transition: all 0.5s;
    margin-bottom: 2em;
    padding: 0;
}

.service-area .single-service {
    position: relative;
    border: 1px solid #f3f3f3;
    transition: all 0.5s;
}

.service-area .ser-img {
    position: relative;
    overflow: hidden;
}

.service-area .ser-img img {
    border: 8px solid #fff;
    border-bottom: none;
    width: 100%;
    height: auto;
    display: block;
    transition: all 0.3s;
}

.service-area .ser-img img:hover {
    border: 8px solid #c9593f;
    border-bottom: none;
}

.service-area .box-border {
    display: block;
    position: relative;
    content: " ";
    text-shadow: none;
    width: 45%;
    border-radius: 0 50px 50px 0;
    border-style: solid;
    border-width: 4px;
    border-color: #000;
    transition: all 0.5s;
}

.service-area .single-service:hover .box-border {
    width: 100%;
    transition: all 1s;
    border-radius: 0;
}

.service-area .service-title-box {
    position: relative;
    padding: 30px 14px 10px 14px;
}

.service-area .service-title-box h3 {
    font-size: 20px;
    font-weight: 700;
    transition: all 0.5s ease;
    position: relative;
    margin: 0;
}

.service-area .service-title-box h4 {
    font-size: 20px;
    font-weight: 700;
    transition: all 0.5s ease;
    position: relative;
    margin: 0;
    color: #333;
}

.service-area .service-title-box p {
    font-size: 16px;
    padding: 10px 0;
    line-height: 24px;
    margin: 0;
    color: #666;
}

.service-area .btn5 a {
    clip-path: polygon(25% 0, 100% 0%, 100% 100%, 0 100%);
    float: right;
    position: relative;
    overflow: hidden;
    display: inline-block;
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    padding: 12px 20px 12px 50px;
    color: #fff;
    transition: all 0.5s;
    border-radius: 0px;
    cursor: pointer;
    text-align: center;
    z-index: 9;
    max-width: 12em;
    word-break: break-all;
    background-color: #c9593f;
    border: none;
}

.service-area .btn5 a i {
    font-size: 17px;
    padding-left: 5px;
    font-weight: 600;
    top: 2px;
    position: relative;
}

.service-area .btn5 a:after {
    position: absolute;
    content: "";
    width: 100%;
    height: 0;
    bottom: 0;
    left: 0;
    z-index: -1;
    border-radius: 0px;
    background-color: #000000;
    transition: all 0.3s ease;
}

.service-area .btn5 a:hover {
    color: #ffffff;
    background-color: #000000;
    text-decoration: none;
}

.service-area .btn5 a:hover:after {
    top: 0;
    height: 100%;
}

.service-area .btn5 a:active {
    top: 2px;
}

/* Services Section */
.services-section {
    padding: 80px 0;
}

.service-card {
    background: #fff;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: transform 0.3s;
    height: 100%;
}

.service-card:hover {
    transform: translateY(-5px);
}

.service-icon {
    margin-bottom: 30px;
}

.service-icon img {
    max-width: 80px;
    height: auto;
}

.service-content h4 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
}

.service-content p {
    color: #666;
    line-height: 1.6;
}

/* Certifications Section */
.certifications-section {
    padding: 80px 0;
    background: #fff;
}

.certification-content h3 {
    font-size: 32px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
}

.certification-list {
    list-style: none;
    padding: 0;
    margin: 30px 0;
}

.certification-list li {
    padding: 10px 0;
    font-size: 16px;
    color: #666;
}

.certification-list li i {
    color: #4CAF50;
    margin-right: 10px;
    font-size: 18px;
}

.certification-image img {
    max-width: 300px;
    height: auto;
}

/* Product Features Section */
.product-features {
    padding: 80px 0;
}

.feature-card {
    padding: 30px 20px;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    margin-bottom: 20px;
}

.feature-icon img {
    width: 80px;
    height: 80px;
    object-fit: contain;
}

.feature-card h5 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
}

/*=====================================*/
/*=============about Section=================*/
/*=====================================*/

#about {
    position: relative;
    overflow: hidden;
    padding: 80px 0;
}

#about .section-title h2 {
    font-size: 36px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
}

#about .section-title .sub-title {
    font-size: 18px;
    font-weight: 700;
    color: #c9593f;
    margin-bottom: 10px;
}

#about .border-title {
    border-color: #c9593f;
    display: block;
    margin-top: 0px;
    content: " ";
    text-shadow: none;
    width: 80px;
    border-radius: 50px;
    border-style: solid none none;
    border-top-width: 6px;
    border-left-width: initial;
    border-right-width: initial;
    border-bottom-width: initial;
    position: relative;
}

#about .htext {
    font-size: 16px;
    line-height: 24px;
    color: #666;
}

#about .about-area-data h4 {
    font-size: 30px;
    margin: 0;
    font-weight: 600;
    color: #333;
}

#about .about-area-data p {
    font-size: 16px;
    line-height: 24px;
    color: #666;
    margin-bottom: 10px;
}

#about .about-area-data h4 {
    color: #333;
    margin-bottom: 15px;
}

#about .aboutus-single {
    margin-top: 2em;
    display: flex;
    align-items: flex-start;
    margin-bottom: 30px;
}

/*===============================*/
#about .hi-icon {
    text-align: center;
    color: #ffffff;
    background: #c9593f;
    width: 50px;
    height: 50px;
    line-height: 50px;
    border-radius: 50px;
    font-size: 22px;
    display: inline-block;
    cursor: pointer;
    margin: 0 15px 0 0;
    position: relative;
    z-index: 1;
    -webkit-transition: -webkit-transform ease-out 0.1s, background 0.2s;
    -moz-transition: -moz-transform ease-out 0.1s, background 0.2s;
    transition: transform ease-out 0.1s, background 0.2s;
}

#about .aboutus-single:hover .hi-icon {
    -webkit-transform: scale(1.0);
    -moz-transform: scale(1.0);
    -ms-transform: scale(1.0);
    transform: scale(1.0);
}
#about .hi-icon span:after {
    content: "";
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: transparent;
    transform: rotate(45deg);
    z-index: -1;
    transition: all 0.5s ease-in-out 0s;
}

#about .aboutus-single:hover .hi-icon span:after {
    transform: rotate(-45deg);
    background: #000000;
}
#about .aboutus-single:hover .hi-icon span{
    font-size: 20px;
}
#about .aboutus-single:hover .hi-icon {
    width: 50px;
    height: 50px;
}

#about .aboutus-single:hover .hi-icon {
    color: #c9593f;
    background: none;
}

#about .about-img img {
    width: 100%;
    height: auto;
    border-radius: 5px;
}

/*----------------------------------------*/
/*   Footer Area
/*----------------------------------------*/
.footer-area .fborder {
    /*border-top: 3em solid #fff;*/
    top: 0;
    left:0 ;
    right: 0;
    bottom: 0;
    opacity: 0.3;
    background: #000;
    position: absolute;
    width: 100%;
}
.footer-area p { word-break: break-word;}
.widget-area ul.sub-menu:before,.widget-area ul.sub-menu:after,
.footer-area ul.sub-menu:before,.footer-area ul.sub-menu:after{display: none;}
.gallery-icon.landscape {margin: 6px;}
.footer-area {
    position: relative;
    overflow: hidden;
    background: #c9593f;
    padding: 20px 0;
}
.footer-area .gallery-icon img{height: 85px;}
/*.footer-area .f1,
.footer-area .top-area {
    border-bottom: 1px solid #fff;
}*/
.footer-area li {display: block;margin-bottom: 20px;font-size: 15px;font-weight: 300; }
.footer-area li a {
    font-weight: 300;
    text-decoration: none;
    font-size: 16px;
}
.footer-area ul {margin: 0 0 15px 00px;}
.footer-area p {
    margin-bottom: 18px;
    font-weight: 300;
    line-height: 1.8;
    font-size: 16px;
}
.footer-area .current_page_item > a:before{display: none;}
.footer-area .current_page_item > a{
    background: none;
    font-weight: 500;
}
#footer.footer-area .widget-title {
    letter-spacing: 0px;
    color: #feffff;
    margin-bottom: 1.5em;
    font-family: inherit;
    font-size: 22px;
    font-weight: 400;
}
#footer.footer-area .widget-title:after {
    content: "";
    display: block;
    width: 35%;
    height: 3px;
    margin: 0;
    position: relative;
    top: 8px;
    background: #d3d3d3;
}
.footer-area .social-profile-icons ul{float: left;margin: 0;text-align: center;}
.footer-area .social-profile-icons ul li{
    display: inline-block;
    margin: 9px;
    width: 50px;
    height: 50px;
    line-height: 62px;
    border-radius: 10px;
    /* border: 1px solid #fff; */
    background: #000;
    transition: all 0.5s;
}
.footer-area .social-profile-icons ul li i{
    text-align: center;
    font-size: 28px;
    transition: all 0.5s;
}
.footer-area .social-profile-icons ul li:hover i{
    transform: rotateY(180deg);
    transition: all 0.5s;
}
.footer-area .social-profile-icons ul li:before{
    display: none;
}
.footer-area .widget {margin-bottom: 1.5em;}
.footer-bottom-area ul li {display: inline-block;margin-left: 40px;margin-bottom: 0;position: relative;}
.footer-area .bottom-area {
    padding: 15px 0;
    background: #c9593f;
    margin-top: 0;
}
.footer-area .bottom-area ul { margin-bottom: 0;}
.footer-area .widget.widget_recent_entries li a {font-size: 16px;}
.footer-text, .footer-text a {
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    word-break: break-word;
    background: #8cc63f;
    padding: 15px 5px;
    transition: all 0.5s;
    color: #fff;
    margin: 0;
}
.footer-area .f-contact,
.footer-area .f-contact a {
    font-size: 16px;
    font-weight: 400;
    word-break: break-word;
    transition: all 0.3s;
    color: #fff;
}
.footer-area .f-contact-inn{
    margin: 15px 0px;
    display: flex;
    align-items: center;
}
.footer-area .f-contact i{
    font-size: 18px;
    color: #fff;
    margin-right: 15px;
    width: 20px;
    text-align: center;
}
.footer-area .pd-0{padding: 0;}
.footer-area .bottom-area li a:hover, .social-profile-icons li a:hover{border-bottom: 0;}
.footer-area .bottom-area li:hover a::after, .footer-area .bottom-area li:hover a:after {width: 20px;}
.footer-area .bottom-area li a:after, .footer-area .bottom-area li a:after {position: absolute;width: 0;height: 2px;background: #ff8b00;content: "";bottom: -5px;left: 2px;transition: .3s;}
.footer-area .bottom-area li.current_page_item a:after{position: absolute;width: 20px;height: 2px;background: #ff8b00;content: "";bottom: -5px;left: 2px;transition: .3s;}

.footer-area .bottom-area .footer-link {
    text-align: right;
}
.footer-area  ul {
    list-style: none;
    margin: 10px 0 0 0px;
    padding: 0;
    position: relative;
    background: none !important;
    box-shadow: none !important;
    display: block;
    z-index: 1;
}

.single-footer ul li:before {
    content: "\f178";
    font-family: 'Fontawesome';
    font-size: 18px;
    padding-right: 10px;
    font-weight: 400;
}
.footer-area .f1 i {
    font-size: 25px;
}
.footer-area input[type="text"], .footer-area input[type="email"], .footer-area input[type="url"], .footer-area input[type="password"], .footer-area input[type="search"], .footer-area input[type="number"], .footer-area input[type="tel"], .footer-area input[type="range"], .footer-area input[type="date"], .footer-area input[type="month"], .footer-area input[type="week"], .footer-area input[type="time"], .footer-area input[type="datetime"], .footer-area input[type="datetime-local"], .footer-area input[type="color"], .footer-area textarea,
.footer-area select{
    border: none;
    font-size: 15px;
    height: 55px;
    /* width: 100%; */
    margin-bottom: 10px;
    padding: 5px 10px;
    font-style: normal;
    font-weight: 400;
    background: #ccc;
}
.footer-area input[type="submit"] {
        background: none;
    border: 2px solid;
    font-size: 16px;
    font-weight: 600;
    border: none;
    padding: 0px;
    padding: 0;
    color: #FFF;
    -moz-transition: all 0.3s ease;
    border-radius: 0;
    box-shadow: none;
}
 .footer-area .widget_calendar tfoot tr td a,
 .footer-area .s-footer .textwidget p a {
        background: none;
}
.footer-area input[type="submit"]{
    display: inline-block;
    background: #e1e1e1;
    padding: 18px 30px;
    font-size: 16px;
    font-weight: 600;
    background-color: #fff;
    color: #1d62b8;
    position: relative;
    /* text-indent: -999px; */
    border-radius: 0;
    transition: all 0.5s;
}
/*.footer-area .wpcf7:after {
    content: "\f1d8";
    position: absolute;
    font-size: 18px;
    color: #fff;
    background-repeat: no-repeat;
    font-family: 'Fontawesome';
    text-align: center;
    right: 26px;
    margin: 0;
    padding-top: 15px;
    z-index: 99;
}*/
.footer-area .widget_calendar tfoot tr td a,
.footer-area .s-footer .textwidget p a {
    background: none;
    font-size: 13px;
    font-weight: 600;
}
 .footer-area .widget_calendar tfoot tr td a:hover,
.footer-area .s-footer .textwidget p a:hover{
    opacity: 0.6;
    background: none;
}
.footer-area div.wpcf7 input[type="file"] {font-size: 15px;font-weight: 500;width: 100%;margin-bottom: 30px;}
.footer-area select option{font-weight: 400;}

.footer-area table tr {text-align: center;line-height: 28px;}
.footer-area table td {font-size: 15px;padding: 5px;}
.footer-area .widget_calendar table thead tr th{text-align: center;}
.footer-area fieldset {
    padding-top: 0;
}
footer#footer .widget_calendar thead { background: transparent;box-shadow: 0 0px 3px 0 #88828252;}
.footer-area figure.gallery-item img:hover{opacity: 0.6;}
.footer-area p span {color: #fe900f;font-weight: 400;}

.footer-area .widget_recent_entries ul li {line-height: 25px;}
.single-footer-1 {
    margin-bottom: 2.5em;
}
.single-footer-4{text-align: left;}
.footer_area-img img {
    width: 100%;
    height: 100%;
    border-radius: 5px;
}
.footer_area-img {max-width: 80%;padding-top: 10px;}

.footer_facility-text {
    padding: 5px 2em 15px;
    background: #e7e7e7;
    border: 1px solid #d3d3d3;
    border-right: 0;
    font-size: 15px;
    color: #4c4c4c;
    text-align: center;
}
.footer-facility-area .col-md-4.pd-0:last-child .footer_facility-text{
    border-right: 1px solid #d3d3d3;
}
.footer_facility-text i.fa {
      padding: 0;
    border-radius: 0%;
    width: 35px;
    height: 27px;
    line-height: 27px;
    text-align: center;
    font-size: 27px;
    border: none;
    top: 5px;
    position: relative;
    color: #575757;
}
.footer-area .midix {
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear;
    -webkit-animation-duration: 5s;
    animation-duration: 5s;
}

/* Additional Footer Styles for the specific design */
.footer-area .f-contact .row {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.footer-area .f-contact-inn a {
    color: #fff;
    text-decoration: none;
    display: flex;
    align-items: center;
}

.footer-area .f-contact-inn a:hover {
    opacity: 0.8;
}

.footer-area .f-contact-inn span {
    color: #fff;
    font-size: 16px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .footer-area .f-contact .row {
        flex-direction: column;
        text-align: center;
    }

    .footer-area .f-contact-inn {
        margin: 10px 0;
        justify-content: center;
    }
}

/* Back to Top */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: #8cc63f;
    color: #fff;
    text-align: center;
    line-height: 50px;
    border-radius: 50%;
    display: none;
    transition: all 0.3s;
    z-index: 999;
}

.back-to-top:hover {
    background: #a84832;
    color: #fff;
    text-decoration: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-slider {
        margin-top: 80px;
    }

    .hero-slider .item img {
        height: 300px;
    }

    .section-title h2 {
        font-size: 28px;
    }

    .product-content,
    .service-card {
        padding: 20px;
    }

    .gallery-item img {
        height: 150px;
    }
}

/* Product Gallery Styles */
.product-gallery {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 10px;
    margin: 30px 0;
}

.product-gallery h3 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 30px;
}

.product-gallery .gallery-item {
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
}

.product-gallery .gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.product-gallery .gallery-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 5px;
}

.product-gallery .gallery-caption h5 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 10px;
}

.product-gallery .gallery-caption p {
    color: #6c757d;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 0;
}

/* Enhanced Product Gallery Styles */
.product-gallery .gallery-item {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    margin-bottom: 30px;
}

.product-gallery .gallery-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.product-gallery .gallery-image {
    position: relative;
    overflow: hidden;
}

.product-gallery .gallery-image img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-gallery .gallery-item:hover .gallery-image img {
    transform: scale(1.05);
}

.product-gallery .gallery-content {
    padding: 25px;
}

.product-gallery .gallery-content h4 {
    color: #2c3e50;
    font-weight: 600;
    font-size: 18px;
    margin-bottom: 15px;
    line-height: 1.3;
}

.product-gallery .gallery-content p {
    color: #666;
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 0;
}

/* Section Title Enhancement */
.product-gallery .section-title h2 {
    color: #2c3e50;
    font-weight: 700;
    font-size: 32px;
    margin-bottom: 15px;
}

.product-gallery .section-title .lead {
    color: #666;
    font-size: 16px;
    margin-bottom: 0;
}

.product-gallery .border-line {
    width: 60px;
    height: 3px;
    background: linear-gradient(45deg, #c9593f, #4dccc6);
    margin: 0 auto 30px;
    border-radius: 2px;
}

/* Product Video Styles */
.product-video {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 10px;
    margin: 30px 0;
}

.product-video video {
    max-height: 400px;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

@media (max-width: 576px) {
    .main-header {
        padding: 10px 0;
    }

    .logo img {
        max-height: 40px;
    }

    .section-title h2 {
        font-size: 24px;
    }

    .hero-slider .item img {
        height: 250px;
    }
}

/* Animation Classes */
.animate-fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.animate-fade-in.animated {
    opacity: 1;
    transform: translateY(0);
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, #8cc63f 0%, #c9593f 100%);
    color: #fff;
    padding: 150px 0 80px;
    margin-top: 120px;
}

.page-title {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 20px;
}

.breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
}

.breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.breadcrumb-item.active {
    color: #fff;
}

/* Product Pages */
.product-hero {
    padding: 60px 0;
}

.product-hero-image img {
    max-height: 400px;
    object-fit: cover;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.product-details {
    padding: 80px 0;
}

.product-content h2 {
    font-size: 32px;
    font-weight: 600;
    color: #333;
}

.product-content h3 {
    font-size: 24px;
    font-weight: 500;
}

.product-description p {
    font-size: 16px;
    line-height: 1.8;
    color: #666;
    margin-bottom: 20px;
}

.feature-list {
    list-style: none;
    padding: 0;
}

.feature-list li {
    padding: 8px 0;
    font-size: 16px;
    color: #666;
}

.feature-list i {
    margin-right: 10px;
}

.product-sidebar {
    padding-left: 30px;
}

.product-info-card,
.contact-card {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.product-info-card h4,
.contact-card h4 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.info-item:last-child {
    border-bottom: none;
}

.contact-card .contact-info p {
    margin-bottom: 10px;
    color: #666;
}

.contact-card .contact-info i {
    color: #c9593f;
    margin-right: 10px;
    width: 20px;
}

.btn-block {
    width: 100%;
    margin-top: 20px;
}

/* Process Cards */
.process-card {
    background: #fff;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s;
    height: 100%;
}

.process-card:hover {
    transform: translateY(-5px);
}

.process-card img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 8px;
}

.process-card h5 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 15px 0 10px;
}

.process-card p {
    color: #666;
    font-size: 14px;
}

/* Step Numbers for About Page */
.step-number {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: #c9593f;
    color: #fff;
    border-radius: 50%;
    line-height: 40px;
    text-align: center;
    font-weight: 600;
    margin-bottom: 20px;
}

.process-step img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 10px;
}

/* Company Features */
.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.feature-item i {
    margin-right: 15px;
    font-size: 18px;
}

.feature-item span {
    font-size: 16px;
    color: #666;
}

/* Contact Page Styles */
.contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.contact-item img {
    margin-right: 15px;
}

.contact-link {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    text-decoration: none;
}

.contact-link:hover {
    color: #c9593f;
}

.social-link {
    display: block;
    color: #666;
    text-decoration: none;
    margin-bottom: 10px;
    transition: color 0.3s;
}

.social-link:hover {
    color: #c9593f;
}

.social-link i {
    margin-right: 10px;
    width: 20px;
}

.contact-form {
    background: #fff;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.form-group label {
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
}

.form-control {
    border: 2px solid #eee;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.form-control:focus {
    border-color: #c9593f;
    box-shadow: 0 0 0 0.2rem rgba(201, 89, 63, 0.25);
}

.form-control.error {
    border-color: #dc3545;
}

/* Lightbox Styles */
.lightbox-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.lightbox-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
}

.lightbox-content img {
    max-width: 100%;
    max-height: 100%;
    border-radius: 10px;
}

.lightbox-close {
    position: absolute;
    top: -40px;
    right: -40px;
    background: #fff;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-size: 20px;
    cursor: pointer;
    color: #333;
}

/* Hero Slider Owl Carousel Custom Styles */
#hero-carousel.owl-carousel {
    display: block;
}

.hero-slider .owl-theme .owl-dots {
    position: absolute;
    bottom: 50%;
    right: 0px;
    display: none;
}

.hero-slider .owl-nav {
    display: block;
}

.hero-slider .owl-nav .owl-prev {
    position: absolute;
    left: 90px;
    top: 45%;
    opacity: 0;
    -webkit-transition: all 0.4s ease-out;
    transition: all 0.4s ease-out;
    background: rgba(0, 0, 0, 0.3) !important;
    color: #ffffff !important;
    width: 50px;
    line-height: 50px;
    height: 50px;
    display: block;
    z-index: 1000;
    border-radius: 0%;
    cursor: pointer;
    border: none;
}

.hero-slider .owl-nav .owl-next {
    position: absolute;
    right: 90px;
    top: 45%;
    opacity: 0;
    -webkit-transition: all 0.4s ease-out;
    transition: all 0.4s ease-out;
    background: rgba(0, 0, 0, 0.3) !important;
    color: #ffffff !important;
    width: 50px;
    height: 50px;
    line-height: 50px;
    display: block;
    z-index: 1000;
    border-radius: 0%;
    cursor: pointer;
    border: none;
}

.hero-slider .owl-nav .owl-prev span,
.hero-slider .owl-nav .owl-next span {
    font-size: 65px;
    color: #fff;
    line-height: 45px;
    font-weight: 300;
}

.hero-slider .owl-nav .owl-prev:focus,
.hero-slider .owl-nav .owl-next:focus {
    outline: 0;
}

.hero-slider .owl-nav .owl-prev:hover,
.hero-slider .owl-nav .owl-next:hover {
    background: #FFFFFF !important;
    color: #000000 !important;
}

.hero-slider:hover .owl-prev {
    left: 0px;
    opacity: 1;
}

.hero-slider:hover .owl-next {
    right: 0px;
    opacity: 1;
}

/* General Owl Carousel Styles */
.owl-nav {
    position: absolute;
    top: 50%;
    width: 100%;
    transform: translateY(-50%);
}

.owl-nav button {
    position: absolute;
    background: rgba(255, 255, 255, 0.8);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 20px;
    color: #333;
    transition: all 0.3s;
}

.owl-nav .owl-prev {
    left: 20px;
}

.owl-nav .owl-next {
    right: 20px;
}

.owl-nav button:hover {
    background: #c9593f;
    color: #fff;
}

.owl-dots {
    text-align: center;
    margin-top: 20px;
}

.owl-dots .owl-dot {
    display: inline-block;
    width: 12px;
    height: 12px;
    background: #ddd;
    border-radius: 50%;
    margin: 0 5px;
    transition: background 0.3s;
}

.owl-dots .owl-dot.active {
    background: #c9593f;
}

/* About Page Specific Styles */
.page-main-header {
    background: #8cc63f;
    color: white;
    padding: 30px 0;
    margin-top: 150px;
    text-align: center;
}

.ht-main-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

#innerpage-box {
    padding: 40px 0;
    background: #f8f9fa;
}

.inner_contentbox {
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    overflow: hidden;
}

.innerpage-whitebox {
    padding: 40px;
}

.wp-block-heading {
    color: #c9593f;
    font-weight: 700;
    margin: 30px 0;
}

.wp-block-image figure {
    margin: 20px 0;
}

.wp-block-image img {
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.wp-block-image img:hover {
    transform: scale(1.02);
}

.wp-block-image figcaption {
    font-weight: 600;
    color: #333;
    margin-top: 10px;
    font-size: 14px;
}

.row.align-items-center {
    margin: 40px 0;
    padding: 20px 0;
    border-bottom: 1px solid #eee;
}

.row.align-items-center:last-child {
    border-bottom: none;
}

.col-md-4 p {
    color: #c9593f;
    font-weight: 600;
}

.col-md-8 p {
    line-height: 1.8;
    color: #555;
}

.col-md-8 p strong {
    color: #c9593f;
}

/* About Page Responsive Styles */
@media (max-width: 768px) {
    .ht-main-title {
        font-size: 2rem;
    }

    .innerpage-whitebox {
        padding: 20px;
    }

    .col-md-4 p {
        text-align: left !important;
        margin-bottom: 15px;
    }
}

/* Contact Page Styles */
.contact-section {
    min-height: 60vh;
}

.contact-item {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #c9593f;
}

.contact-item i {
    font-size: 18px;
    width: 20px;
    flex-shrink: 0;
}

.contact-details {
    flex: 1;
}

.contact-label {
    font-weight: 600;
    color: #c9593f;
    margin-right: 8px;
}

.contact-text {
    color: #333;
    line-height: 1.6;
}

.contact-item:hover {
    background: #e9ecef;
    transition: background-color 0.3s ease;
}

.contact-text a,
.contact-text.text-decoration-none {
    color: #333;
    transition: color 0.3s ease;
}

.contact-text a:hover,
.contact-text.text-decoration-none:hover {
    color: #c9593f;
}

.contact-methods img {
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
}