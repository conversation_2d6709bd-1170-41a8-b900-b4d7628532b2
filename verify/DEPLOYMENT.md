# YK-EcoPack 静态网站部署指南

## 部署前准备

### 1. 下载必需的第三方库文件

在部署之前，需要下载以下第三方库文件到对应目录：

#### CSS 文件 (assets/css/)
```bash
# Bootstrap 5
wget https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css -O assets/css/bootstrap.min.css

# Font Awesome
wget https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css -O assets/css/font-awesome.min.css

# Animate.css
wget https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css -O assets/css/animate.css

# Owl Carousel
wget https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css -O assets/css/owl.carousel.min.css
```

#### JavaScript 文件 (assets/js/)
```bash
# jQuery
wget https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.0/jquery.min.js -O assets/js/jquery.min.js

# Bootstrap JS
wget https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js -O assets/js/bootstrap.bundle.min.js

# Owl Carousel JS
wget https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js -O assets/js/owl.carousel.min.js

# WOW.js
wget https://cdnjs.cloudflare.com/ajax/libs/wow/1.1.2/wow.min.js -O assets/js/wow.min.js
```

### 2. 复制图片资源

从原WordPress网站文件中复制图片到对应目录：

#### 主要图片文件映射
```bash
# 公司Logo
cp "YK-EcoPack - Your Packs Factory_files/cropped-（长款）YK-logo.png" assets/images/logo.png

# 轮播图片
cp "YK-EcoPack - Your Packs Factory_files/3.-Grocery-Bags-Compostable-1.jpg" assets/images/slider/grocery-bags.jpg
cp "YK-EcoPack - Your Packs Factory_files/Kitchen-Garbage-Bags-Compostable.jpg" assets/images/slider/kitchen-garbage-bags.jpg
cp "YK-EcoPack - Your Packs Factory_files/Pet-Waste-Bags.jpg" assets/images/slider/pet-waste-bags.jpg

# 产品缩略图
cp "YK-EcoPack - Your Packs Factory_files/Grocery-Bags-Small-Image.jpg" assets/images/products/grocery-bags-small.jpg
cp "YK-EcoPack - Your Packs Factory_files/Kitchen-Garbage-Bags-Small-Image.jpg" assets/images/products/kitchen-garbage-bags-small.jpg
cp "YK-EcoPack - Your Packs Factory_files/Pet-Waste-Bags-Small-Iamge.jpg" assets/images/products/pet-waste-bags-small.jpg

# 工厂图片
cp "YK-EcoPack - Your Packs Factory_files/YK-EcoPack-Factory-Gate-scaled.jpg" assets/images/factory/factory-gate.jpg
cp "YK-EcoPack - Your Packs Factory_files/film-blowing-biodegradable-1.jpg" assets/images/factory/film-blowing.jpg
cp "YK-EcoPack - Your Packs Factory_files/Film-Blowing-Machine-Setting--scaled.jpg" assets/images/factory/temperature-control.jpg
cp "YK-EcoPack - Your Packs Factory_files/film-blowing-workshop-with-green-rolls.jpg" assets/images/factory/printing-workshop.jpg
cp "YK-EcoPack - Your Packs Factory_files/Film-Rolling-biodegradable.jpg" assets/images/factory/film-rolling.jpg
cp "YK-EcoPack - Your Packs Factory_files/Ready-to-Ship-scaled.jpg" assets/images/factory/warehouse.jpg

# 服务图标
cp "YK-EcoPack - Your Packs Factory_files/package-service.png" assets/images/services/package-service.png
```

### 3. 创建缺失的目录
```bash
mkdir -p assets/images/slider
mkdir -p assets/images/products
mkdir -p assets/images/factory
mkdir -p assets/images/services
mkdir -p assets/images/certifications
mkdir -p assets/images/contact
mkdir -p assets/images/icons
```

## 部署选项

### 选项1: 静态文件服务器部署

#### Apache 部署
1. 将整个 `static-website` 目录复制到 Apache 的 `htdocs` 或 `www` 目录
2. 确保 Apache 已启用 `mod_rewrite` 模块
3. 创建 `.htaccess` 文件（可选）：
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.html [QSA,L]

# 启用Gzip压缩
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# 设置缓存
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
</IfModule>
```

#### Nginx 部署
1. 将文件复制到 Nginx 的 web 根目录
2. 配置 Nginx：
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/static-website;
    index index.html;

    # Gzip压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # 静态文件缓存
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 主页面
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

### 选项2: CDN 部署

#### 使用 Netlify
1. 将代码推送到 Git 仓库
2. 连接 Netlify 到仓库
3. 设置构建命令（无需构建）
4. 设置发布目录为根目录

#### 使用 Vercel
1. 安装 Vercel CLI: `npm i -g vercel`
2. 在项目目录运行: `vercel`
3. 按提示完成部署

#### 使用 GitHub Pages
1. 将代码推送到 GitHub 仓库
2. 在仓库设置中启用 GitHub Pages
3. 选择源分支和目录

### 选项3: 云服务器部署

#### 使用 Docker
创建 `Dockerfile`：
```dockerfile
FROM nginx:alpine
COPY . /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

构建和运行：
```bash
docker build -t yk-ecopack-website .
docker run -p 80:80 yk-ecopack-website
```

## 性能优化

### 1. 图片优化
```bash
# 使用 imagemin 压缩图片
npm install -g imagemin-cli imagemin-mozjpeg imagemin-pngquant

# 压缩 JPEG
imagemin assets/images/**/*.jpg --out-dir=assets/images --plugin=mozjpeg

# 压缩 PNG
imagemin assets/images/**/*.png --out-dir=assets/images --plugin=pngquant
```

### 2. CSS/JS 压缩
```bash
# 安装压缩工具
npm install -g uglifycss uglify-js

# 压缩 CSS
uglifycss assets/css/style.css > assets/css/style.min.css

# 压缩 JS
uglifyjs assets/js/main.js -o assets/js/main.min.js
```

### 3. 启用 HTTP/2
确保服务器支持 HTTP/2 以提高加载速度。

## 测试清单

部署后请检查以下项目：

### 功能测试
- [ ] 所有页面正常加载
- [ ] 导航菜单工作正常
- [ ] 轮播图正常播放
- [ ] 联系表单提交功能
- [ ] 社交媒体链接正确
- [ ] 返回顶部按钮工作

### 响应式测试
- [ ] 桌面端显示正常
- [ ] 平板端显示正常
- [ ] 手机端显示正常
- [ ] 导航菜单在移动端正常折叠

### 性能测试
- [ ] 页面加载速度 < 3秒
- [ ] 图片正常显示
- [ ] CSS/JS 文件正常加载
- [ ] 无控制台错误

### SEO 检查
- [ ] 所有页面有正确的 title 和 meta description
- [ ] 图片有 alt 属性
- [ ] 页面结构语义化
- [ ] 内部链接正确

## 维护建议

1. **定期备份**：定期备份网站文件和数据库（如有）
2. **安全更新**：定期更新第三方库到最新版本
3. **性能监控**：使用工具监控网站性能
4. **内容更新**：定期更新产品信息和公司动态
5. **SEO 优化**：持续优化搜索引擎排名

## 故障排除

### 常见问题

1. **图片不显示**
   - 检查图片路径是否正确
   - 确认图片文件已正确复制

2. **样式不生效**
   - 检查 CSS 文件路径
   - 确认第三方 CSS 库已下载

3. **JavaScript 功能不工作**
   - 检查浏览器控制台错误
   - 确认 jQuery 和其他依赖库已加载

4. **移动端显示异常**
   - 检查 viewport meta 标签
   - 测试响应式断点

## 联系支持

如遇到部署问题，请联系：
- 技术支持：<EMAIL>
- 电话：+86 150 1527 1728
