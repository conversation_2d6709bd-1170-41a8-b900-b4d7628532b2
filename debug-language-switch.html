<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Language Switch Debug</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .debug-section { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .language-switcher { margin: 20px 0; }
        .btn { padding: 8px 16px; margin: 0 5px; text-decoration: none; border: 1px solid #007bff; color: #007bff; border-radius: 4px; }
        .btn:hover { background: #007bff; color: white; }
        .active { background: #28a745 !important; color: white !important; border-color: #28a745 !important; }
        #debug-log { background: #000; color: #0f0; padding: 10px; font-family: monospace; height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>Language Switch Debug Tool</h1>
    
    <div class="debug-section">
        <h3>Test Language Switcher</h3>
        <div class="language-switcher Rqst-btn">
            <a href="#" class="btn btn-outline-primary btn-sm">中文版</a>
            <a href="#" class="btn btn-outline-primary btn-sm">EN</a>
        </div>
    </div>
    
    <div class="debug-section">
        <h3>Debug Information</h3>
        <div id="debug-info">
            <p><strong>Current URL:</strong> <span id="current-url"></span></p>
            <p><strong>Current Path:</strong> <span id="current-path"></span></p>
            <p><strong>Current Page:</strong> <span id="current-page"></span></p>
            <p><strong>jQuery Loaded:</strong> <span id="jquery-status"></span></p>
        </div>
    </div>
    
    <div class="debug-section">
        <h3>Debug Log</h3>
        <div id="debug-log"></div>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <!-- Load jQuery first -->
    <script src="en_website/assets/js/jquery.min.js"></script>
    
    <script>
        // Debug logging function
        function debugLog(message) {
            const log = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `[${timestamp}] ${message}\n`;
            log.scrollTop = log.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            document.getElementById('debug-log').innerHTML = '';
        }
        
        // Initialize debug info
        document.addEventListener('DOMContentLoaded', function() {
            debugLog('DOM Content Loaded');
            
            // Update debug info
            document.getElementById('current-url').textContent = window.location.href;
            document.getElementById('current-path').textContent = window.location.pathname;
            document.getElementById('current-page').textContent = window.location.pathname.split('/').pop() || 'index.html';
            document.getElementById('jquery-status').textContent = typeof $ !== 'undefined' ? 'Yes ✓' : 'No ✗';
            
            debugLog('jQuery status: ' + (typeof $ !== 'undefined' ? 'Loaded' : 'Not loaded'));
            
            // Test if we can find the language switcher
            if (typeof $ !== 'undefined') {
                const switchers = $('.language-switcher a');
                debugLog('Found ' + switchers.length + ' language switcher buttons');
                
                // Add click handlers
                $('.language-switcher a').click(function(e) {
                    e.preventDefault();
                    const lang = $(this).text().trim();
                    debugLog('Language button clicked: ' + lang);
                    
                    // Get current page name
                    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
                    debugLog('Current page: ' + currentPage);
                    
                    // Get the base path
                    const currentPath = window.location.pathname;
                    debugLog('Current path: ' + currentPath);
                    
                    // Try to determine base path
                    let basePath = '';
                    if (currentPath.includes('/en_website/')) {
                        basePath = currentPath.substring(0, currentPath.lastIndexOf('/en_website/'));
                        debugLog('Detected en_website, base path: ' + basePath);
                    } else if (currentPath.includes('/cn_website/')) {
                        basePath = currentPath.substring(0, currentPath.lastIndexOf('/cn_website/'));
                        debugLog('Detected cn_website, base path: ' + basePath);
                    } else {
                        // Fallback: assume we're in root directory
                        basePath = window.location.origin + window.location.pathname.substring(0, window.location.pathname.lastIndexOf('/'));
                        debugLog('No language directory detected, using fallback base path: ' + basePath);
                    }
                    
                    // Language switching logic
                    if (lang === '中文版') {
                        const chineseUrl = basePath + '/cn_website/' + currentPage;
                        debugLog('Would redirect to Chinese: ' + chineseUrl);
                        
                        // Check if target exists (simplified check)
                        debugLog('Attempting to switch to Chinese version...');
                        // Uncomment the next line to actually redirect
                        // window.location.href = chineseUrl;
                        
                        // Visual feedback instead
                        $(this).addClass('active').siblings().removeClass('active');
                        setTimeout(() => {
                            $('.language-switcher a').removeClass('active');
                        }, 2000);
                        
                    } else if (lang === 'EN') {
                        const englishUrl = basePath + '/en_website/' + currentPage;
                        debugLog('Would redirect to English: ' + englishUrl);
                        
                        // Visual feedback
                        $(this).addClass('active').siblings().removeClass('active');
                        setTimeout(() => {
                            $('.language-switcher a').removeClass('active');
                        }, 2000);
                    }
                });
                
                debugLog('Language switcher initialized successfully');
            } else {
                debugLog('ERROR: jQuery not loaded, cannot initialize language switcher');
            }
        });
    </script>
</body>
</html>
